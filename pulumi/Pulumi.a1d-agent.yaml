# Pulumi configuration for A1D Agent deployment
# This file contains the configuration for deploying the A1D Agent AI service

config:
  # Infrastructure stack reference
  infraStackRef: "ethan-huo-org/a1d-infra/dev"
  
  # Application configuration
  appName: "a1d-agent"
  containerImage: "a1dazureacr.azurecr.io/a1d-agent:latest"
  environment: "production"
  
  # Resource allocation for AI workloads
  cpu: 1.0        # Increased for AI processing
  memory: 2       # Increased for AI models and data processing
  port: 4111      # Match application's exposed port
  
  # Scaling configuration - Always-on service
  minReplicas: 1  # Keep at least 1 instance running
  maxReplicas: 3  # Allow scaling for high load
  scaleToZero: false  # Disable scale-to-zero for always-on service
  
  # Domain configuration
  usesEnvironmentLevelDomains: true
  
  # Secrets (set these using: pulumi config set --secret <key> <value>)
  # postgresUrl: "postgresql://..."  # Set via: pulumi config set --secret postgresUrl "postgresql://..."
  # convexUrl: "https://..."         # Set via: pulumi config set --secret convexUrl "https://..."
  
  # Additional AI service specific configurations
  # Add more secrets as needed for your integrations:
  # minimaxApiKey: "..."
  # anthropicApiKey: "..."
  # openaiApiKey: "..."
  # etc.
