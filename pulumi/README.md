# A1D Agent Azure 部署指南

这个 Pulumi 模板已经针对 A1D Agent AI 服务进行了优化配置，支持常驻运行和 AI 工作负载。

## 主要改进

### 🔄 常驻运行配置
- **最小副本数**: 1 (始终保持至少一个实例运行)
- **禁用缩放到零**: 确保服务始终可用
- **增强的健康检查**: 适配 AI 服务的启动时间

### 🚀 AI 工作负载优化
- **CPU**: 1.0 核心 (相比原来的 0.5 核心)
- **内存**: 2GB (相比原来的 1GB)
- **端口**: 4111 (匹配应用的实际端口)
- **扩展规则**: CPU 和并发请求双重触发

### 🔐 环境变量和密钥管理
- 自动配置 `POSTGRES_URL` 和 `CONVEX_URL`
- 支持通过 Pulumi 配置管理敏感信息
- 预留了 AI 服务集成的密钥配置

## 快速开始

### 1. 设置必要的密钥

```bash
# 进入 pulumi 目录
cd pulumi

# 选择或创建 stack
pulumi stack select a1d-agent  # 或 pulumi stack init a1d-agent

# 设置必要的密钥
pulumi config set --secret postgresUrl "postgresql://user:pass@host:port/database"
pulumi config set --secret convexUrl "https://your-deployment.convex.cloud"
```

### 2. 可选：设置 AI 服务密钥

```bash
# 根据你的集成需求设置相应的 API 密钥
pulumi config set --secret minimaxApiKey "your-minimax-key"
pulumi config set --secret anthropicApiKey "your-anthropic-key"
pulumi config set --secret openaiApiKey "your-openai-key"
pulumi config set --secret groqApiKey "your-groq-key"
# ... 其他 API 密钥
```

### 3. 自定义配置 (可选)

```bash
# 自定义应用名称
pulumi config set appName "my-a1d-agent"

# 自定义资源配置
pulumi config set cpu 2.0
pulumi config set memory 4
pulumi config set maxReplicas 5

# 设置环境
pulumi config set environment "production"
```

### 4. 部署

使用提供的部署脚本：

```bash
# 从项目根目录运行
./scripts/deploy-a1d-agent.sh
```

或手动部署：

```bash
cd pulumi
pulumi up
```

## 配置参数

| 参数 | 默认值 | 说明 |
|------|--------|------|
| `appName` | `a1d-agent` | 应用名称 |
| `containerImage` | `a1dazureacr.azurecr.io/a1d-agent:latest` | 容器镜像 |
| `cpu` | `1.0` | CPU 核心数 |
| `memory` | `2` | 内存 (GB) |
| `port` | `4111` | 应用端口 |
| `minReplicas` | `1` | 最小副本数 |
| `maxReplicas` | `3` | 最大副本数 |
| `scaleToZero` | `false` | 是否允许缩放到零 |

## 健康检查

应用配置了三种健康检查：

- **Startup Probe**: 启动检查，允许最多 2 分钟启动时间
- **Liveness Probe**: 存活检查，每 30 秒检查一次
- **Readiness Probe**: 就绪检查，每 15 秒检查一次

所有检查都使用 `/health` 端点。

## 扩展规则

- **HTTP 并发**: 超过 5 个并发请求时扩展
- **CPU 使用率**: 超过 70% 时扩展

## 输出信息

部署完成后，你将获得以下端点：

- **健康检查**: `https://{appName}.whiteboardanimation.ai/health`
- **Mastra API**: `https://{appName}.whiteboardanimation.ai/mastra`
- **主要 URL**: `https://{appName}.whiteboardanimation.ai`

## 故障排除

### 常见问题

1. **部署失败**: 检查是否设置了所有必要的密钥
2. **健康检查失败**: 确保应用在端口 4111 上提供 `/health` 端点
3. **内存不足**: 考虑增加 `memory` 配置

### 查看日志

```bash
# 查看部署状态
pulumi stack output

# 查看 Azure 容器应用日志
az containerapp logs show --name {appName} --resource-group {resourceGroup}
```
