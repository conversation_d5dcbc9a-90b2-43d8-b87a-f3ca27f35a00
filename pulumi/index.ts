import * as azure from '@pulumi/azure-native'
import * as pulumi from '@pulumi/pulumi'

// Configuration
const config = new pulumi.Config()
const infraStackRef = config.require('infraStackRef')

// Application configuration - A1D Agent specific
const appName = config.get('appName') || 'a1d-agent'
const containerImage =
  config.get('containerImage') ||
  'a1dazureacr.azurecr.io/a1d-agent:latest'
const cpu = config.getNumber('cpu') || 1.0 // Increased for AI workloads
const memory = config.getNumber('memory') || 2 // Increased for AI workloads
const port = config.getNumber('port') || 4111 // Match Dockerfile EXPOSE

// Scaling configuration - Always-on for AI agent services
const minReplicas = config.getNumber('minReplicas') || 1 // Always keep at least 1 running
const maxReplicas = config.getNumber('maxReplicas') || 3 // Allow scaling for high load
const scaleToZero = config.getBoolean('scaleToZero') ?? false // Disable scale-to-zero for always-on service
const usesEnvironmentLevelDomains =
  config.getBoolean('usesEnvironmentLevelDomains') ?? true

// Reference the shared infrastructure stack
const infraStack = new pulumi.StackReference(infraStackRef)

// Get shared resources from infrastructure stack
const resourceGroupName = infraStack.getOutput('resourceGroupName')
const containerAppsEnvironmentId = infraStack.getOutput(
  'containerAppsEnvironmentId',
)

const acrLoginServer = infraStack.getOutput('acrLoginServer')
const acrName = infraStack.getOutput('acrName')

// Get ACR admin credentials
const acrCredentials = resourceGroupName.apply((rgName) =>
  azure.containerregistry.listRegistryCredentialsOutput({
    resourceGroupName: rgName,
    registryName: acrName,
  }),
)

// Create Container App - with environment-level domains, no custom domain setup needed
const containerApp = new azure.app.ContainerApp(appName, {
  resourceGroupName: resourceGroupName,
  containerAppName: appName,
  managedEnvironmentId: containerAppsEnvironmentId,
  configuration: {
    registries: [
      {
        server: acrLoginServer,
        username: acrCredentials.apply((creds) => creds.username!),
        passwordSecretRef: 'acr-password',
      },
    ],
    secrets: [
      {
        name: 'acr-password',
        value: acrCredentials.apply((creds) => creds.passwords![0]!.value!),
      },
      // Add application secrets - these should be provided via Pulumi config
      {
        name: 'postgres-url',
        value: config.requireSecret('postgresUrl'),
      },
      {
        name: 'convex-url',
        value: config.requireSecret('convexUrl'),
      },
    ],
    ingress: {
      external: true,
      targetPort: port,
      transport: azure.app.IngressTransportMethod.Http,
      // No custom domain needed - app will automatically get <appName>.whiteboardanimation.ai
      traffic: [
        {
          weight: 100,
          latestRevision: true,
        },
      ],
    },
  },
  template: {
    // Always-on configuration for AI agent services
    scale: {
      minReplicas: minReplicas, // Keep at least 1 replica running
      maxReplicas: maxReplicas, // Allow scaling for AI workloads
      rules: [
        {
          name: 'http-scaling-rule',
          http: {
            metadata: {
              concurrentRequests: '5', // Scale up when > 5 concurrent requests (AI tasks are resource intensive)
            },
          },
        },
        {
          name: 'cpu-scaling-rule',
          custom: {
            type: 'cpu',
            metadata: {
              type: 'Utilization',
              value: '70', // Scale up when CPU > 70%
            },
          },
        },
      ],
    },
    containers: [
      {
        name: appName,
        image: containerImage,
        resources: {
          cpu: cpu,
          memory: `${memory}Gi`,
        },
        env: [
          {
            name: 'NODE_ENV',
            value: 'production',
          },
          // Add environment variables from infrastructure stack if available
          // These would typically be set via Pulumi config or stack references
          {
            name: 'POSTGRES_URL',
            secretRef: 'postgres-url',
          },
          {
            name: 'CONVEX_URL',
            secretRef: 'convex-url',
          },
        ],
        probes: [
          {
            type: azure.app.Type.Liveness,
            httpGet: {
              path: '/health',
              port: port,
              scheme: azure.app.Scheme.HTTP,
            },
            initialDelaySeconds: 60, // Increased for AI service startup
            periodSeconds: 30,
            timeoutSeconds: 10, // Increased timeout for AI workloads
            failureThreshold: 5, // More tolerant for AI services
          },
          {
            type: azure.app.Type.Readiness,
            httpGet: {
              path: '/health',
              port: port,
              scheme: azure.app.Scheme.HTTP,
            },
            initialDelaySeconds: 30, // Increased for AI service startup
            periodSeconds: 15,
            timeoutSeconds: 10, // Increased timeout
            failureThreshold: 3,
          },
          {
            type: azure.app.Type.Startup,
            httpGet: {
              path: '/health',
              port: port,
              scheme: azure.app.Scheme.HTTP,
            },
            initialDelaySeconds: 10,
            periodSeconds: 10,
            timeoutSeconds: 10,
            failureThreshold: 12, // Allow up to 2 minutes for startup
          },
        ],
      },
    ],
  },
  tags: {
    Project: 'a1d-agent',
    Environment: config.get('environment') || 'development',
    Component: 'ai-agent-service',
    Platform: 'azure',
    Service: 'mastra-ai-agent',
    AlwaysOn: 'true',
  },
})

// Exports - simplified for environment-level domains
export const containerAppName = containerApp.name
export const containerAppFqdn = containerApp.configuration?.apply(
  (c) => c?.ingress?.fqdn,
)
export const environmentDomainUrl = pulumi.interpolate`https://${appName}.whiteboardanimation.ai`
export const environmentHealthCheck = pulumi.interpolate`https://${appName}.whiteboardanimation.ai/health`
export const environmentMastraEndpoint = pulumi.interpolate`https://${appName}.whiteboardanimation.ai/mastra`
export const environmentApiEndpoint = pulumi.interpolate`https://${appName}.whiteboardanimation.ai/api`

// Export application info
export const applicationInfo = {
  name: appName,
  automaticDomain: pulumi.interpolate`${appName}.whiteboardanimation.ai`,
  containerImage: containerImage,
  minReplicas: minReplicas, // Always-on AI agent service
  maxReplicas: maxReplicas, // Allow scaling for AI workloads
  cpu: cpu,
  memory: memory,
  port: port,
  usesEnvironmentLevelDomains: usesEnvironmentLevelDomains,
  scaleToZero: scaleToZero, // Disabled for always-on AI service
  serviceType: 'ai-agent',
  framework: 'mastra',
  alwaysOn: true,
}

// Export default Azure Container Apps URL as backup
export const defaultAppUrl = containerApp.configuration?.apply((c) =>
  c?.ingress?.fqdn ? `https://${c.ingress.fqdn}` : undefined,
)
